import { DateTimePicker } from '@/components/forms/fields';
import { useOrganisation } from '@/components/OrganisationProvider';
import SidePane from '@/components/SidePane';
import { useFileUpload } from '@/hooks/useFileUpload';
import { useFormValidation } from '@/hooks/useFormValidation';
import { mapFormDataToActivity2 } from '@/util/activityMapper';
import {
    ActivityFile,
    ActivityFormData,
    Format,
    MediaFormData
} from '@quarterback/types';
import { log } from '@quarterback/util/gcp';
import { useCallback, useEffect, useReducer, useState } from 'react';

import { NewTextField } from './components/NewTextField';
import { NewTextArea } from './components/NewTextArea';
import { FormatTabs } from './components/FormatTabs';
import { MediaUpload } from './components/MediaUpload';
import NewPersonDropdown from './components/NewPersonDropdown';
import NewSourceDropdown from './components/NewSourceDropdown';
import RenderActivityForm from '../components/RenderActivityForm';
import { formReducer, initialFormState } from '../reducers/activityFormReducer';

export default function ManualActivityFormFactory({
    addingActivity,
    setAddingActivity,
    onSubmit
}: {
    onSubmit: (data: any) => Promise<any>;
    addingActivity: boolean;
    setAddingActivity: (value: boolean) => void;
}) {
    const [formData, dispatch] = useReducer(formReducer, initialFormState);
    const [isSaving, setIsSaving] = useState(false);
    const organisation = useOrganisation();
    const [savingError, setSavingError] = useState<Error | undefined>(undefined);
    const [files, setFiles] = useState<File[]>([]);

    const { uploadFiles, isUploading, uploadedFiles, setUploadedFiles } = useFileUpload();

    const { validate, errors, setErrors, validateField } = useFormValidation(
        ActivityFormData,
        formData
    );

    useEffect(() => {
        setErrors({});
    }, [formData.format, formData.source, setErrors]);

    const handleFormSubmit = useCallback(async () => {
        setSavingError(undefined);
        if (validate()) {
            try {
                setIsSaving(true);

                // First, upload any files that haven't been uploaded yet
                let tempUploadedFiles: ActivityFile[] = [];
                if (files.length > 0) {
                    tempUploadedFiles = await uploadFiles(files);
                    if (tempUploadedFiles.length !== files.length) {
                        throw new Error('Some files failed to upload');
                    }
                }

                formData.files = tempUploadedFiles;

                // Create the activity
                const activity2Data = mapFormDataToActivity2(formData, {
                    symbol: organisation.selected?.entity.symbol,
                    exchange: organisation.selected?.entity.exchange
                });

                // Submit the activity
                await onSubmit([activity2Data]);

                // Reset the form
                dispatch({ type: 'RESET' });
                setErrors({});
                setFiles([]);
                setUploadedFiles([]);
                setAddingActivity(false);
            } catch (error: any) {
                log('ERROR', 'Error submitting activity:', { error });
                setSavingError(error);
            } finally {
                setIsSaving(false);
            }
        } else {
            // Focus the first field with an error
            const firstErrorField = Object.keys(errors)[0];
            const element = document.querySelector(`[name="${firstErrorField}"]`);
            if (element) {
                element.scrollIntoView({ behavior: 'smooth', block: 'center' });
                (element as HTMLElement).focus();
            }
        }
    }, [
        validate,
        formData,
        organisation.selected?.entity.symbol,
        organisation.selected?.entity.exchange,
        onSubmit,
        setErrors,
        setAddingActivity,
        errors,
        files,
        uploadFiles,
        setUploadedFiles,
        setFiles
    ]);

    // Handle format change
    const handleFormatChange = useCallback((value: string) => {
        dispatch({
            type: 'SET_FORMAT',
            payload: value as Format
        });
    }, []);

    const handleDateChange = useCallback(
        (date: Date) => {
            if (!isSaving) {
                dispatch({
                    type: 'UPDATE_FIELD',
                    payload: {
                        field: 'posted',
                        value: date.toISOString()
                    }
                });
                validateField('posted');
            }
        },
        [isSaving, validateField]
    );

    const handleDateBlur = useCallback(() => {
        validateField('posted');
    }, [validateField]);

    return (
        <SidePane
            breadCrumbs={['Manual Activity', 'New activity']}
            open={addingActivity}
            confirmButtonText={isSaving ? 'Saving...' : 'Add activity'}
            cancelButtonText="Cancel"
            isDisabledConfirm={isSaving || isUploading}
            isDsabledCancel={isSaving || isUploading}
            onConfirm={handleFormSubmit}
            onCancel={() => {
                if (!isSaving) {
                    setAddingActivity(false);
                    dispatch({ type: 'RESET' });
                    setErrors({});
                    setSavingError(undefined);
                }
            }}>
            <div className="flex flex-col gap-3">
                {isSaving && (
                    <div className="bg-blue-50 p-3 rounded-md mb-2 flex items-center">
                        <div className="flex space-x-2 items-center text-blue-700">
                            <div className="size-1.5 bg-blue-600 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
                            <div className="size-1.5 bg-blue-600 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
                            <div className="size-1.5 bg-blue-600 rounded-full animate-bounce"></div>
                            <span className="ml-2 text-sm font-medium">
                                Saving activity...
                            </span>
                        </div>
                    </div>
                )}

                {savingError && (
                    <div className="bg-red-50 p-3 rounded-md mb-2 flex items-center">
                        <div className="flex space-x-2 items-center text-red-700">
                            <span className="ml-2 text-sm font-medium">
                                Error saving activity: {savingError.message}
                            </span>
                        </div>
                    </div>
                )}

                {/* Format Selection Tabs */}
                <div className={isSaving ? 'pointer-events-none opacity-70' : ''}>
                    <FormatTabs
                        value={formData.format}
                        onChange={handleFormatChange}
                        error={errors['format']}
                    />
                </div>

                {/* Activity Title */}
                <NewTextField
                    label="Activity Title"
                    value={formData.title || ''}
                    onChange={(value) =>
                        dispatch({
                            type: 'UPDATE_FIELD',
                            payload: { field: 'title', value }
                        })
                    }
                    placeholder="Activity Title"
                    error={errors['title']}
                    subtitle="Today | 11:00 AWST"
                />

                {/* Activity Body */}
                <NewTextArea
                    label=""
                    value={formData.body}
                    onChange={(value) =>
                        dispatch({
                            type: 'UPDATE_FIELD',
                            payload: { field: 'body', value }
                        })
                    }
                    placeholder="This might be the case, but it's just a word of caution, especially given the track record of other J&J people to come in with the same promises. You only need to look at history to understand big Pharma's stance on psychedelics in the past, which is a separate discussion all together You don't find the appointment unusual?"
                    rows={6}
                    error={errors['body']}
                />

                {/* Activity URL */}
                <NewTextField
                    label="Activity URL"
                    value={formData.url || ''}
                    onChange={(value) =>
                        dispatch({
                            type: 'UPDATE_FIELD',
                            payload: { field: 'url', value }
                        })
                    }
                    placeholder="Paste URL"
                    error={errors['url']}
                    type="url"
                />

                {/* Conditional Source/Author Selection */}
                <div className={isSaving ? 'pointer-events-none opacity-70' : ''}>
                    {formData.format === 'media' ? (
                        <NewSourceDropdown
                            key={`source-${formData.format}`}
                            value={(formData as MediaFormData)?.newsSource?.url || ''}
                            dispatch={isSaving ? () => {} : dispatch}
                            error={errors['source']}
                        />
                    ) : (
                        <NewPersonDropdown
                            key={`author-${formData.format}-${formData.source}`}
                            value={formData.author?.key || ''}
                            dispatch={isSaving ? () => {} : dispatch}
                            error={errors['author']}
                            sourceFilter={formData.source}
                        />
                    )}
                </div>

                {/* Media Upload */}
                <MediaUpload
                    label="Media"
                    files={uploadedFiles}
                    onChange={setUploadedFiles}
                    error={errors['files']}
                />

                {/* Additional Form Fields based on format */}
                <RenderActivityForm
                    formData={formData}
                    dispatch={isSaving ? () => {} : dispatch}
                />

                {/* Posted Date */}
                <div className="space-y-4">
                    <DateTimePicker
                        label="Posted Date"
                        value={formData.posted ? new Date(formData.posted) : null}
                        onChange={handleDateChange}
                        onBlur={handleDateBlur}
                        error={errors['posted']}
                        required={true}
                        timezone="Australia/Sydney"
                    />
                </div>
            </div>
        </SidePane>
    );
}
