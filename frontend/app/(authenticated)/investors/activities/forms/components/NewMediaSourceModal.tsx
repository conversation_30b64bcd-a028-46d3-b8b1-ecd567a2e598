import {
    Dialog,
    DialogPanel,
    DialogTitle,
    Transition,
    TransitionChild
} from '@headlessui/react';
import { XMarkIcon, NewspaperIcon, LinkIcon, DocumentTextIcon, PhotoIcon } from '@heroicons/react/24/outline';
import React, { useState, useCallback } from 'react';
import { NewTextField } from './NewTextField';
import { NewTextArea } from './NewTextArea';
import Button from '@/components/ui/Button';
import { useFormValidation } from '@/hooks/useFormValidation';
import { NewsSource } from '@quarterback/types';
import * as z from 'zod';

// Define the form data schema for news source
const NewsSourceFormData = z.object({
    name: z.string().min(1, 'Name is required'),
    url: z.string().url('Must be a valid URL'),
    logo: z.string().url('Must be a valid URL').optional().or(z.literal('')),
    notes: z.string().optional()
});

type NewsSourceFormData = z.infer<typeof NewsSourceFormData>;

interface NewMediaSourceModalProps {
    open: boolean;
    setOpen: (value: boolean) => void;
    onSuccess: (source: NewsSource) => void;
}

export default function NewMediaSourceModal({
    open,
    setOpen,
    onSuccess
}: NewMediaSourceModalProps) {
    const [formData, setFormData] = useState<NewsSourceFormData>({
        name: '',
        url: '',
        logo: '',
        notes: ''
    });

    const [isLoading, setIsLoading] = useState(false);
    const { validate, errors, setErrors } = useFormValidation(NewsSourceFormData, formData);

    const handleChange = useCallback((field: keyof NewsSourceFormData, value: any) => {
        setFormData((prev) => ({
            ...prev,
            [field]: value
        }));
        // Clear error when user starts typing
        if (errors[field]) {
            setErrors(prev => ({ ...prev, [field]: undefined }));
        }
    }, [errors, setErrors]);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        
        if (!validate()) {
            return;
        }

        setIsLoading(true);
        try {
            // TODO: Implement API call to create news source
            // For now, we'll simulate the creation
            const newSource: NewsSource = {
                url: formData.url,
                name: formData.name,
                logo: formData.logo || undefined
            };
            
            onSuccess(newSource);
            handleClose();
        } catch (error) {
            console.error('Failed to create media source:', error);
        } finally {
            setIsLoading(false);
        }
    };

    const handleClose = () => {
        setOpen(false);
        // Reset form
        setFormData({
            name: '',
            url: '',
            logo: '',
            notes: ''
        });
        setErrors({});
    };

    return (
        <Dialog className="relative z-50" open={open} onClose={handleClose}>
            <div className="fixed inset-0 bg-black bg-opacity-25" />
            
            <div className="fixed inset-0 overflow-y-auto">
                <div className="flex min-h-full items-center justify-center p-4 text-center">
                    <DialogPanel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                        <div className="flex items-center justify-between mb-6">
                            <div className="flex items-center gap-2">
                                <div className="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                                    <NewspaperIcon className="w-4 h-4 text-red-600" />
                                </div>
                                <DialogTitle as="h3" className="text-lg font-semibold text-gray-900">
                                    New Media Source
                                </DialogTitle>
                            </div>
                            <button
                                type="button"
                                onClick={handleClose}
                                className="text-gray-400 hover:text-gray-600 transition-colors">
                                <XMarkIcon className="w-5 h-5" />
                            </button>
                        </div>

                        <form onSubmit={handleSubmit} className="space-y-4">
                            <NewTextField
                                label="Media Name"
                                value={formData.name}
                                onChange={(value) => handleChange('name', value)}
                                placeholder="TheWestAustralian"
                                error={errors['name']}
                                required
                            />

                            <NewTextField
                                label="Publication URL"
                                value={formData.url}
                                onChange={(value) => handleChange('url', value)}
                                placeholder="Set URL"
                                error={errors['url']}
                                icon={<LinkIcon className="w-4 h-4" />}
                                required
                            />

                            <NewTextField
                                label="Publication Logo/Favicon"
                                value={formData.logo || ''}
                                onChange={(value) => handleChange('logo', value)}
                                placeholder="Upload Media"
                                error={errors['logo']}
                                icon={<PhotoIcon className="w-4 h-4" />}
                                subtitle="Enter logo URL"
                            />

                            <NewTextArea
                                label="Notes"
                                value={formData.notes || ''}
                                onChange={(value) => handleChange('notes', value)}
                                placeholder="Add notes"
                                rows={3}
                                error={errors['notes']}
                                icon={<DocumentTextIcon className="w-4 h-4" />}
                            />

                            <div className="flex justify-end gap-3 pt-4">
                                <Button
                                    type="button"
                                    variant="secondary"
                                    onClick={handleClose}
                                    disabled={isLoading}>
                                    Cancel
                                </Button>
                                <Button
                                    type="submit"
                                    variant="primary"
                                    disabled={isLoading}>
                                    {isLoading ? 'Creating...' : 'Create Media Source'}
                                </Button>
                            </div>
                        </form>
                    </DialogPanel>
                </div>
            </div>
        </Dialog>
    );
}
