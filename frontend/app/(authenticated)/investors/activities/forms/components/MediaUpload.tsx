import { useState, useCallback } from 'react';
import {
    PaperClipIcon,
    ArrowDownTrayIcon,
    TrashIcon,
    XMarkIcon
} from '@heroicons/react/24/outline';
import classNames from 'classnames';
import { ActivityFile } from '@quarterback/types';

interface MediaUploadProps {
    label?: string;
    files: ActivityFile[];
    onChange: (files: ActivityFile[]) => void;
    error?: string;
    icon?: React.ReactNode;
}

export function MediaUpload({ label, files, onChange, error, icon }: MediaUploadProps) {
    const [isDragOver, setIsDragOver] = useState(false);

    const handleFileSelect = useCallback(
        (selectedFiles: FileList | null) => {
            if (!selectedFiles) return;

            const newFiles: ActivityFile[] = Array.from(selectedFiles).map(
                (file, index) => ({
                    fileName: file.name,
                    fileSize: file.size,
                    fileType: file.type,
                    storagePath: `temp-${Date.now()}-${index}`, // Temporary path for identification
                    signedUrl: URL.createObjectURL(file), // Temporary URL for preview
                    // Store the actual file for upload (this is a custom property for our form)
                    file: file as any
                })
            );

            onChange([...files, ...newFiles]);
        },
        [files, onChange]
    );

    const handleDrop = useCallback(
        (e: React.DragEvent) => {
            e.preventDefault();
            setIsDragOver(false);
            handleFileSelect(e.dataTransfer.files);
        },
        [handleFileSelect]
    );

    const handleDragOver = useCallback((e: React.DragEvent) => {
        e.preventDefault();
        setIsDragOver(true);
    }, []);

    const handleDragLeave = useCallback((e: React.DragEvent) => {
        e.preventDefault();
        setIsDragOver(false);
    }, []);

    const handleRemoveFile = useCallback(
        (storagePath: string) => {
            const updatedFiles = files.filter((file) => file.storagePath !== storagePath);
            onChange(updatedFiles);
        },
        [files, onChange]
    );

    const handleDownloadFile = useCallback((file: ActivityFile) => {
        if (file.signedUrl) {
            const link = document.createElement('a');
            link.href = file.signedUrl;
            link.download = file.fileName;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    }, []);

    const formatFileSize = (bytes: number) => {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    return (
        <div className="space-y-1">
            {label && (
                <div className="flex items-center gap-2">
                    {icon && <span className="text-gray-500">{icon}</span>}
                    <label className="block text-sm font-medium text-gray-900">
                        {label}
                    </label>
                </div>
            )}

            {/* Upload Area */}
            <div
                className={classNames(
                    'border-2 border-dashed rounded-lg p-4 text-center transition-colors',
                    isDragOver
                        ? 'border-indigo-400 bg-indigo-50'
                        : error
                          ? 'border-red-300 bg-red-50'
                          : 'border-gray-200 bg-gray-50 hover:border-gray-300'
                )}
                onDrop={handleDrop}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}>
                <input
                    type="file"
                    multiple
                    onChange={(e) => handleFileSelect(e.target.files)}
                    className="hidden"
                    id="file-upload"
                />

                <label htmlFor="file-upload" className="cursor-pointer">
                    <PaperClipIcon className="mx-auto h-8 w-8 text-gray-400 mb-2" />
                    <p className="text-sm text-gray-600">
                        <span className="font-medium text-indigo-600 hover:text-indigo-500">
                            Upload Media
                        </span>
                        {' or drag and drop'}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">PNG, JPG, PDF up to 10MB</p>
                </label>
            </div>

            {/* File List */}
            {files.length > 0 && (
                <div className="space-y-2 mt-3">
                    {files.map((file) => (
                        <div
                            key={file.storagePath}
                            className="flex items-center justify-between p-3 bg-white border border-gray-200 rounded-lg">
                            <div className="flex items-center gap-3 flex-1 min-w-0">
                                <PaperClipIcon className="h-4 w-4 text-gray-400 flex-shrink-0" />
                                <div className="min-w-0 flex-1">
                                    <p className="text-sm font-medium text-gray-900 truncate">
                                        {file.fileName}
                                    </p>
                                    {file.fileSize && (
                                        <p className="text-xs text-gray-500">
                                            {formatFileSize(file.fileSize)}
                                        </p>
                                    )}
                                </div>
                            </div>

                            <div className="flex items-center gap-1">
                                <button
                                    type="button"
                                    onClick={() => handleDownloadFile(file)}
                                    className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                                    title="Download">
                                    <ArrowDownTrayIcon className="h-4 w-4" />
                                </button>
                                <button
                                    type="button"
                                    onClick={() => handleRemoveFile(file.storagePath!)}
                                    className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                                    title="Delete">
                                    <TrashIcon className="h-4 w-4" />
                                </button>
                            </div>
                        </div>
                    ))}
                </div>
            )}

            {error && <p className="text-xs text-red-600 mt-1">{error}</p>}
        </div>
    );
}
