import { Format } from '@quarterback/types';
import classNames from 'classnames';
import { ChatBubbleLeftEllipsisIcon, NewspaperIcon } from '@heroicons/react/24/outline';

export interface FormatTabsProps {
    value: Format;
    onChange: (format: Format) => void;
    error?: string;
}

const FORMAT_TABS = [
    {
        id: 'chatter' as Format,
        label: 'Chatter',
        icon: ChatBubbleLeftEllipsisIcon,
        description: 'Social media posts and discussions'
    },
    {
        id: 'media' as Format,
        label: 'Source',
        icon: NewspaperIcon,
        description: 'News articles and media content'
    }
];

export function FormatTabs({ value, onChange, error }: FormatTabsProps) {
    return (
        <div className="space-y-1">
            <div className="flex rounded-lg border border-gray-200 bg-gray-50 p-1">
                {FORMAT_TABS.map((tab) => {
                    const Icon = tab.icon;
                    const isActive = value === tab.id;
                    
                    return (
                        <button
                            key={tab.id}
                            type="button"
                            onClick={() => onChange(tab.id)}
                            className={classNames(
                                'flex-1 flex items-center justify-center gap-2 px-3 py-2 text-sm font-medium rounded-md transition-all duration-200',
                                isActive
                                    ? 'bg-white text-gray-900 shadow-sm border border-gray-200'
                                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                            )}>
                            <Icon className="h-4 w-4" />
                            {tab.label}
                        </button>
                    );
                })}
            </div>
            {error && <p className="text-xs text-red-600 mt-1">{error}</p>}
        </div>
    );
}
