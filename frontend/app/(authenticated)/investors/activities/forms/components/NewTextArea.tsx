import { TextAreaProps } from '@quarterback/types';
import classNames from 'classnames';

export interface NewTextAreaProps extends Omit<TextAreaProps, 'onChange'> {
    onChange: (value: string) => void;
    icon?: React.ReactNode;
    subtitle?: string;
}

export function NewTextArea({
    label,
    value,
    onChange,
    onBlur,
    rows = 4,
    placeholder,
    error,
    required = false,
    icon,
    subtitle
}: NewTextAreaProps) {
    return (
        <div className="space-y-1">
            {label && (
                <div className="flex items-center gap-2">
                    {icon && <span className="text-gray-500">{icon}</span>}
                    <label className="block text-sm font-medium text-gray-900">
                        {label}
                        {required && <span className="text-red-500 ml-1">*</span>}
                    </label>
                </div>
            )}
            {subtitle && (
                <p className="text-xs text-gray-500 -mt-1">{subtitle}</p>
            )}
            <textarea
                className={classNames(
                    'block w-full rounded-lg border px-3 py-2 text-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-colors resize-none',
                    error
                        ? 'border-red-300 bg-red-50 text-red-900 focus:ring-red-500'
                        : 'border-gray-200 bg-white text-gray-900 hover:border-gray-300'
                )}
                rows={rows}
                value={value}
                onChange={(e) => {
                    onChange(e.target.value);
                }}
                onBlur={onBlur}
                placeholder={placeholder}
                required={required}
            />
            {error && <p className="text-xs text-red-600 mt-1">{error}</p>}
        </div>
    );
}
