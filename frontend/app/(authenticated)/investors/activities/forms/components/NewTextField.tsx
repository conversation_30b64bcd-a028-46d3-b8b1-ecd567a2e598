import { TextFieldProps } from '@quarterback/types';
import classNames from 'classnames';

export interface NewTextFieldProps extends Omit<TextFieldProps, 'onChange'> {
    onChange: (value: string) => void;
    icon?: React.ReactNode;
    subtitle?: string;
}

export function NewTextField({
    label,
    value,
    onChange,
    onBlur,
    placeholder,
    min,
    max,
    step,
    error,
    readOnly = false,
    required = false,
    type = 'text',
    icon,
    subtitle
}: NewTextFieldProps) {
    return (
        <div className="space-y-1">
            {label && (
                <div className="flex items-center gap-2">
                    {icon && <span className="text-gray-500">{icon}</span>}
                    <label className="block text-sm font-medium text-gray-900">
                        {label}
                        {required && <span className="text-red-500 ml-1">*</span>}
                    </label>
                </div>
            )}
            {subtitle && (
                <p className="text-xs text-gray-500 -mt-1">{subtitle}</p>
            )}
            <input
                type={type}
                readOnly={readOnly}
                className={classNames(
                    'block w-full rounded-lg border px-3 py-2 text-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-colors',
                    error
                        ? 'border-red-300 bg-red-50 text-red-900 focus:ring-red-500'
                        : 'border-gray-200 bg-white text-gray-900 hover:border-gray-300',
                    readOnly && 'bg-gray-50 cursor-not-allowed'
                )}
                value={value}
                min={min}
                max={max}
                step={step}
                onChange={(e) => {
                    onChange(e.target.value);
                }}
                onBlur={onBlur}
                placeholder={placeholder}
                required={required}
            />
            {error && <p className="text-xs text-red-600 mt-1">{error}</p>}
        </div>
    );
}
