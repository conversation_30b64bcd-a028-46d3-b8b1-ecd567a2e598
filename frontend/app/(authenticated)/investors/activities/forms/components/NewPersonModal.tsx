import {
    Dialog,
    DialogPanel,
    DialogTitle,
    Transition,
    TransitionChild
} from '@headlessui/react';
import { XMarkIcon, UserIcon, LinkIcon, DocumentTextIcon, PhotoIcon } from '@heroicons/react/24/outline';
import React, { useState, useCallback } from 'react';
import { NewTextField } from './NewTextField';
import { NewTextArea } from './NewTextArea';
import Button from '@/components/ui/Button';
import { useFormValidation } from '@/hooks/useFormValidation';
import useAuthorMutation from '@/api/hooks/mutations/useAuthorMutation';
import { Author, AuthorFormData } from '@quarterback/types';

interface NewPersonModalProps {
    open: boolean;
    setOpen: (value: boolean) => void;
    onSuccess: (author: Author) => void;
    sourceFilter?: string;
}

export default function NewPersonModal({
    open,
    setOpen,
    onSuccess,
    sourceFilter
}: NewPersonModalProps) {
    const [formData, setFormData] = useState<AuthorFormData>({
        userId: '',
        name: '',
        source: sourceFilter || 'advfn',
        url: '',
        image: '',
        followers: null,
        following: null,
        notes: ''
    });

    const { validate, errors, setErrors } = useFormValidation(AuthorFormData, formData);
    const { createAuthor, isLoading } = useAuthorMutation();

    const handleChange = useCallback((field: keyof AuthorFormData, value: any) => {
        setFormData((prev) => ({
            ...prev,
            [field]: value
        }));
        // Clear error when user starts typing
        if (errors[field]) {
            setErrors(prev => ({ ...prev, [field]: undefined }));
        }
    }, [errors, setErrors]);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        
        if (!validate()) {
            return;
        }

        try {
            const author = await createAuthor(formData);
            onSuccess(author);
            handleClose();
        } catch (error) {
            console.error('Failed to create author:', error);
        }
    };

    const handleClose = () => {
        setOpen(false);
        // Reset form
        setFormData({
            userId: '',
            name: '',
            source: sourceFilter || 'advfn',
            url: '',
            image: '',
            followers: null,
            following: null,
            notes: ''
        });
        setErrors({});
    };

    return (
        <Dialog className="relative z-50" open={open} onClose={handleClose}>
            <div className="fixed inset-0 bg-black bg-opacity-25" />
            
            <div className="fixed inset-0 overflow-y-auto">
                <div className="flex min-h-full items-center justify-center p-4 text-center">
                    <DialogPanel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                        <div className="flex items-center justify-between mb-6">
                            <div className="flex items-center gap-2">
                                <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                                    <UserIcon className="w-4 h-4 text-orange-600" />
                                </div>
                                <DialogTitle as="h3" className="text-lg font-semibold text-gray-900">
                                    New Person
                                </DialogTitle>
                            </div>
                            <button
                                type="button"
                                onClick={handleClose}
                                className="text-gray-400 hover:text-gray-600 transition-colors">
                                <XMarkIcon className="w-5 h-5" />
                            </button>
                        </div>

                        <form onSubmit={handleSubmit} className="space-y-4">
                            <NewTextField
                                label="Name"
                                value={formData.name}
                                onChange={(value) => handleChange('name', value)}
                                placeholder="Amy M"
                                error={errors['name']}
                                required
                            />

                            <NewTextField
                                label="Username"
                                value={formData.userId}
                                onChange={(value) => handleChange('userId', value)}
                                placeholder="Set Username"
                                error={errors['userId']}
                                required
                            />

                            <NewTextField
                                label="Profile URL"
                                value={formData.url || ''}
                                onChange={(value) => handleChange('url', value)}
                                placeholder="Set Profile URL"
                                error={errors['url']}
                                icon={<LinkIcon className="w-4 h-4" />}
                            />

                            <NewTextArea
                                label="Notes"
                                value={formData.notes || ''}
                                onChange={(value) => handleChange('notes', value)}
                                placeholder="Add notes"
                                rows={3}
                                error={errors['notes']}
                                icon={<DocumentTextIcon className="w-4 h-4" />}
                            />

                            <NewTextField
                                label="Profile Picture"
                                value={formData.image || ''}
                                onChange={(value) => handleChange('image', value)}
                                placeholder="Upload Media"
                                error={errors['image']}
                                icon={<PhotoIcon className="w-4 h-4" />}
                                subtitle="Enter image URL"
                            />

                            <div className="flex justify-end gap-3 pt-4">
                                <Button
                                    type="button"
                                    variant="secondary"
                                    onClick={handleClose}
                                    disabled={isLoading}>
                                    Cancel
                                </Button>
                                <Button
                                    type="submit"
                                    variant="primary"
                                    disabled={isLoading}>
                                    {isLoading ? 'Creating...' : 'Create Person'}
                                </Button>
                            </div>
                        </form>
                    </DialogPanel>
                </div>
            </div>
        </Dialog>
    );
}
