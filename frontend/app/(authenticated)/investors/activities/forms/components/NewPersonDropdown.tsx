import { useCallback, useEffect, useMemo, useState } from 'react';
import { UserIcon, PlusIcon } from '@heroicons/react/24/outline';
import { Author } from '@quarterback/types';
import { FormAction } from '../../reducers/activityFormReducer';
import usePaginatedAuthors from '@/api/hooks/usePaginatedAuthors';
import { NewDropdown, DropdownOption } from './NewDropdown';
import NewPersonModal from './NewPersonModal';

interface NewPersonDropdownProps {
    value: string;
    dispatch: React.Dispatch<FormAction>;
    error?: string;
    sourceFilter?: string;
}

export default function NewPersonDropdown({
    value,
    dispatch,
    error,
    sourceFilter
}: NewPersonDropdownProps) {
    const [isModalOpen, setIsModalOpen] = useState(false);

    const {
        authors,
        handleSearch,
        loadMore,
        hasMore,
        isLoading: isLoadingAuthors,
        refresh
    } = usePaginatedAuthors({
        initialLimit: 20,
        prefetch: true,
        sourceFilter
    });

    // Author options - memoized to prevent unnecessary recalculations
    const authorOptions: DropdownOption[] = useMemo(() => {
        return authors.map((author) => ({
            key: author.key,
            value: author.key,
            label: author.name || author.userId,
            icon: <UserIcon className="w-4 h-4 text-gray-400" />
        }));
    }, [authors]);

    // Handle author change
    const handleAuthorChange = useCallback(
        (authorKey: string) => {
            const selectedAuthor = authors.find(author => author.key === authorKey);
            if (selectedAuthor) {
                dispatch({
                    type: 'SET_AUTHOR',
                    payload: selectedAuthor
                });
            }
        },
        [authors, dispatch]
    );

    // Handle new author creation
    const handleAddNewAuthor = useCallback(() => {
        setIsModalOpen(true);
    }, []);

    // Handle successful author creation
    const handleAuthorCreated = useCallback(
        (author: Author) => {
            dispatch({
                type: 'SET_AUTHOR',
                payload: author
            });
            refresh(); // Refresh the authors list
        },
        [dispatch, refresh]
    );

    // Update selected author when value changes
    useEffect(() => {
        if (value && !authors.some((author) => author.key === value)) {
            // If the value doesn't match any loaded author, try to search for it
            const selectedAuthor = authors.find((author) => author.key === value);
            if (selectedAuthor) {
                handleAuthorChange(selectedAuthor.key);
            }
        }
    }, [value, authors, handleAuthorChange]);

    return (
        <>
            <NewDropdown
                label="People"
                value={value}
                onChange={handleAuthorChange}
                options={authorOptions}
                onSearch={handleSearch}
                onLoadMore={loadMore}
                hasMore={hasMore}
                isLoading={isLoadingAuthors}
                searchPlaceholder="Search..."
                actionButton={{
                    label: 'Create new person',
                    onClick: handleAddNewAuthor
                }}
                error={error}
                icon={<UserIcon className="w-4 h-4" />}
            />

            <NewPersonModal
                open={isModalOpen}
                setOpen={setIsModalOpen}
                onSuccess={handleAuthorCreated}
                sourceFilter={sourceFilter}
            />
        </>
    );
}
