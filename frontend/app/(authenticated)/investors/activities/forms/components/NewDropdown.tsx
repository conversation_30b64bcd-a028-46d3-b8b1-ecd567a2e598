import {
    Combobox,
    ComboboxButton,
    ComboboxInput,
    ComboboxOption,
    ComboboxOptions
} from '@headlessui/react';
import {
    CheckIcon,
    ChevronUpDownIcon,
    MagnifyingGlassIcon,
    PlusIcon
} from '@heroicons/react/24/outline';
import classNames from 'classnames';
import { useCallback, useRef, useState } from 'react';

export interface DropdownOption {
    value: string;
    label: string;
    key?: string;
    icon?: React.ReactNode;
}

export interface NewDropdownProps {
    label?: string;
    value: string;
    onChange: (value: string) => void;
    options: DropdownOption[];
    onSearch?: (searchTerm: string) => void;
    onLoadMore?: () => void;
    hasMore?: boolean;
    isLoading?: boolean;
    searchPlaceholder?: string;
    actionButton?: {
        label: string;
        onClick: () => void;
    };
    error?: string;
    required?: boolean;
    icon?: React.ReactNode;
    subtitle?: string;
}

export function NewDropdown({
    label,
    value,
    onChange,
    options,
    onSearch,
    onLoadMore,
    hasMore,
    isLoading,
    searchPlaceholder = 'Search...',
    actionButton,
    error,
    required = false,
    icon,
    subtitle
}: NewDropdownProps) {
    const [query, setQuery] = useState('');
    const scrollRef = useRef<HTMLDivElement>(null);

    const selectedOption = options.find((option) => option.value === value);

    const filteredOptions = query === '' 
        ? options 
        : options.filter((option) =>
            option.label.toLowerCase().includes(query.toLowerCase())
        );

    const handleInputChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
        const newQuery = event.target.value;
        setQuery(newQuery);
        if (onSearch) {
            onSearch(newQuery);
        }
    }, [onSearch]);

    const handleScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
        const { scrollTop, scrollHeight, clientHeight } = event.currentTarget;
        if (scrollHeight - scrollTop <= clientHeight * 1.5 && hasMore && onLoadMore && !isLoading) {
            onLoadMore();
        }
    }, [hasMore, onLoadMore, isLoading]);

    return (
        <div className="space-y-1">
            {label && (
                <div className="flex items-center gap-2">
                    {icon && <span className="text-gray-500">{icon}</span>}
                    <label className="block text-sm font-medium text-gray-900">
                        {label}
                        {required && <span className="text-red-500 ml-1">*</span>}
                    </label>
                </div>
            )}
            {subtitle && (
                <p className="text-xs text-gray-500 -mt-1">{subtitle}</p>
            )}
            
            <Combobox value={value} onChange={onChange}>
                <div className="relative">
                    <div className="relative">
                        <ComboboxInput
                            className={classNames(
                                'w-full rounded-lg border px-3 py-2 pr-10 text-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-colors',
                                error
                                    ? 'border-red-300 bg-red-50 text-red-900 focus:ring-red-500'
                                    : 'border-gray-200 bg-white text-gray-900 hover:border-gray-300'
                            )}
                            displayValue={() => selectedOption?.label || ''}
                            onChange={handleInputChange}
                            placeholder={searchPlaceholder}
                        />
                        <ComboboxButton className="absolute inset-y-0 right-0 flex items-center pr-2">
                            <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
                        </ComboboxButton>
                    </div>

                    <ComboboxOptions className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-lg bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
                        <div ref={scrollRef} onScroll={handleScroll}>
                            {filteredOptions.map((option) => (
                                <ComboboxOption
                                    key={option.key || option.value}
                                    value={option.value}
                                    className={({ focus, selected }) =>
                                        classNames(
                                            'relative cursor-default select-none py-2 pl-3 pr-9',
                                            {
                                                'bg-indigo-600 text-white': focus,
                                                'text-gray-900': !focus
                                            }
                                        )
                                    }>
                                    {({ focus, selected }) => (
                                        <>
                                            <div className="flex items-center gap-2">
                                                {option.icon && <span className="flex-shrink-0">{option.icon}</span>}
                                                <span className={classNames('block truncate', selected ? 'font-medium' : 'font-normal')}>
                                                    {option.label}
                                                </span>
                                            </div>
                                            {selected && (
                                                <span className={classNames('absolute inset-y-0 right-0 flex items-center pr-4', focus ? 'text-white' : 'text-indigo-600')}>
                                                    <CheckIcon className="h-5 w-5" aria-hidden="true" />
                                                </span>
                                            )}
                                        </>
                                    )}
                                </ComboboxOption>
                            ))}
                            
                            {actionButton && (
                                <div className="border-t border-gray-100 mt-1 pt-1">
                                    <button
                                        type="button"
                                        onClick={actionButton.onClick}
                                        className="w-full flex items-center gap-2 px-3 py-2 text-sm text-indigo-600 hover:bg-indigo-50 transition-colors">
                                        <PlusIcon className="h-4 w-4" />
                                        {actionButton.label}
                                    </button>
                                </div>
                            )}
                            
                            {isLoading && (
                                <div className="px-3 py-2 text-sm text-gray-500">
                                    Loading...
                                </div>
                            )}
                        </div>
                    </ComboboxOptions>
                </div>
            </Combobox>
            
            {error && <p className="text-xs text-red-600 mt-1">{error}</p>}
        </div>
    );
}
