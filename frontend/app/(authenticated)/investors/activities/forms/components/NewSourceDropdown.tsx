import { useCallback, useEffect, useMemo, useState } from 'react';
import { NewspaperIcon, PlusIcon } from '@heroicons/react/24/outline';
import { NewsSource } from '@quarterback/types';
import { FormAction } from '../../reducers/activityFormReducer';
import usePaginatedNewsSources from '@/api/hooks/usePaginatedNewsSources';
import { NewDropdown, DropdownOption } from './NewDropdown';
import NewMediaSourceModal from './NewMediaSourceModal';

interface NewSourceDropdownProps {
    value: string;
    dispatch: React.Dispatch<FormAction>;
    error?: string;
}

export default function NewSourceDropdown({
    value,
    dispatch,
    error
}: NewSourceDropdownProps) {
    const [isModalOpen, setIsModalOpen] = useState(false);

    const {
        sources: newsSources,
        handleSearch,
        loadMore,
        hasMore,
        isLoading: isLoadingSources
    } = usePaginatedNewsSources({
        initialLimit: 20,
        prefetch: true
    });

    // Source options - memoized to prevent unnecessary recalculations
    const sourceOptions: DropdownOption[] = useMemo(() => {
        return newsSources.map((source) => ({
            key: `news-source-${source.url}`,
            value: source.url,
            label: source.name,
            icon: source.logo ? (
                <img src={source.logo} alt={source.name} className="w-4 h-4 rounded" />
            ) : (
                <NewspaperIcon className="w-4 h-4 text-gray-400" />
            )
        }));
    }, [newsSources]);

    // Handle source change
    const handleSourceChange = useCallback(
        (sourceUrl: string) => {
            const selectedSource = newsSources.find(source => source.url === sourceUrl);
            if (selectedSource) {
                dispatch({
                    type: 'SET_SOURCE',
                    payload: { 
                        sourceType: 'media' as any, 
                        sourceData: selectedSource 
                    }
                });
            }
        },
        [newsSources, dispatch]
    );

    // Handle new source creation
    const handleAddNewSource = useCallback(() => {
        setIsModalOpen(true);
    }, []);

    // Handle successful source creation
    const handleSourceCreated = useCallback(
        (source: NewsSource) => {
            dispatch({
                type: 'SET_SOURCE',
                payload: { 
                    sourceType: 'media' as any, 
                    sourceData: source 
                }
            });
            // Note: In a real implementation, you'd want to refresh the sources list
        },
        [dispatch]
    );

    // Update selected source when value changes
    useEffect(() => {
        if (value && !newsSources.some((source) => source.url === value)) {
            handleSearch(value);
        } else if (value) {
            const selectedSource = newsSources.find((source) => source.url === value);
            if (selectedSource) {
                handleSourceChange(selectedSource.url);
            }
        }
    }, [value, newsSources, handleSearch, handleSourceChange]);

    return (
        <>
            <NewDropdown
                label="Source"
                value={value}
                onChange={handleSourceChange}
                options={sourceOptions}
                onSearch={handleSearch}
                onLoadMore={loadMore}
                hasMore={hasMore}
                isLoading={isLoadingSources}
                searchPlaceholder="Search..."
                actionButton={{
                    label: 'Create new',
                    onClick: handleAddNewSource
                }}
                error={error}
                icon={<NewspaperIcon className="w-4 h-4" />}
            />

            <NewMediaSourceModal
                open={isModalOpen}
                setOpen={setIsModalOpen}
                onSuccess={handleSourceCreated}
            />
        </>
    );
}
